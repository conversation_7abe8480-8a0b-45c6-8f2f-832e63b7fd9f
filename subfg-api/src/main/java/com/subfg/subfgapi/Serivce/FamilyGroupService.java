package com.subfg.subfgapi.Serivce;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.subfg.common.constans.FamilyGroupConstants;
import com.subfg.common.exception.BusinessException;
import com.subfg.common.util.IdGeneratorUtil;
import com.subfg.common.util.TimeUtil;
import com.subfg.domain.entity.fg.FgMemberPo;
import com.subfg.domain.entity.fg.SelfBuiltFamilyGroupPo;
import com.subfg.domain.request.createGroupBuyingFamilyGroupReq;
import com.subfg.domain.request.createSelfBuiltFamilyGroupReq;
import com.subfg.repository.mapper.FgMemberMapper;
import com.subfg.repository.mapper.SelfBuiltFamilyGroupMapper;

import cn.dev33.satoken.stp.StpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class FamilyGroupService {

    private final SelfBuiltFamilyGroupMapper selfBuiltFamilyGroupMapper;
    private final FgMemberMapper fgMemberMapper;

    /**
     * 创建自建家庭组
     */
    @Transactional(rollbackFor = Exception.class)
    public String createSelfBuiltFamilyGroup(createSelfBuiltFamilyGroupReq req) {
        log.info("开始创建自建家庭组，请求参数：{}", req);

        // 1. 获取当前登录用户ID
        String currentUserId = StpUtil.getLoginIdAsString();

        // 2. 创建家庭组
        String familyGroupId = createFamilyGroup(req, currentUserId);

        // 3. 创建家庭组成员记录
        createGroupLeaderMember(familyGroupId, currentUserId, req);

        log.info("自建家庭组创建成功，家庭组ID：{}", familyGroupId);
        return familyGroupId;
    }

    /**
     * 创建拼团家庭组
     */
    @Transactional(rollbackFor = Exception.class)
    public String createGroupBuyingFamilyGroup(createGroupBuyingFamilyGroupReq req) {
        log.info("开始创建拼团家庭组，请求参数：{}", req);

        // 1. 获取当前登录用户ID
        String currentUserId = StpUtil.getLoginIdAsString();

        // 2. 验证截止时间
        validateDeadline(req.getDeadline());

        // 3. 创建拼团家庭组
        String familyGroupId = createGroupBuyingFamilyGroupRecord(req, currentUserId);

        // 4. 创建家庭组成员记录
        createGroupLeaderMember(familyGroupId, currentUserId, req);

        log.info("拼团家庭组创建成功，家庭组ID：{}", familyGroupId);
        return familyGroupId;
    }

    /**
     * 验证截止时间
     */
    private void validateDeadline(Long deadline) {
        Long currentTime = TimeUtil.getCurrentTimestamp();
        if (deadline <= currentTime) {
            throw new BusinessException("family.group.deadline.invalid");
        }

        // 截止时间不能超过30天
        long maxDeadline = currentTime + (30L * 24 * 60 * 60 * 1000);
        if (deadline > maxDeadline) {
            throw new BusinessException("family.group.deadline.too.far");
        }
    }

    /**
     * 创建拼团家庭组记录
     */
    private String createGroupBuyingFamilyGroupRecord(createGroupBuyingFamilyGroupReq req, String currentUserId) {
        String familyGroupId = IdGeneratorUtil.generateFamilyGroupId();
        Long currentTime = TimeUtil.getCurrentTimestamp();

        SelfBuiltFamilyGroupPo familyGroup = new SelfBuiltFamilyGroupPo()
            .setFamilyGroupId(familyGroupId)
            .setFamilyGroupName(req.getFamilyGroupName().trim())
            .setDescription(req.getDescription() != null ? req.getDescription().trim() : null)
            .setFamilyGroupStatus(FamilyGroupConstants.Status.RECRUITING) // 设置为招募中状态
            .setProductId(req.getProductId())
            .setRegionId(req.getRegionId())
            .setPlanId(req.getPlanId())
            .setAmount(req.getAmount())
            .setBillingCycle(req.getBillingCycle() != null ? req.getBillingCycle() : FamilyGroupConstants.Defaults.DEFAULT_BILLING_CYCLE)
            .setCurrentMemberCount(FamilyGroupConstants.Defaults.DEFAULT_INITIAL_MEMBER_COUNT) // 初始成员数为1（创建者）
            .setSumVacancy(req.getFamilyGroupTotalVacancy())
            .setGroupLeaderId(currentUserId)
            .setCreateUserId(currentUserId)
            .setCreateTime(currentTime)
            .setUpdateTime(currentTime)
            .setLatestJoinTime(currentTime)
            .setDeadline(req.getDeadline()) // 设置截止时间
            .setSourceType(FamilyGroupConstants.SourceType.DIRECT_CREATE);

        int result = selfBuiltFamilyGroupMapper.insert(familyGroup);
        if (result <= 0) {
            throw new BusinessException(FamilyGroupConstants.ErrorCode.CREATE_FAILED);
        }

        return familyGroupId;
    }

    /**
     * 创建家庭组记录
     */
    private String createFamilyGroup(createSelfBuiltFamilyGroupReq req, String currentUserId) {
        String familyGroupId = IdGeneratorUtil.generateFamilyGroupId();
        Long currentTime = TimeUtil.getCurrentTimestamp();

        SelfBuiltFamilyGroupPo familyGroup = new SelfBuiltFamilyGroupPo()
            .setFamilyGroupId(familyGroupId)
            .setFamilyGroupName(req.getFamilyGroupName().trim())
            .setDescription(req.getDescription() != null ? req.getDescription().trim() : null)
            .setFamilyGroupStatus(FamilyGroupConstants.Status.REVIEWING) // 设置为审核中
            .setProductId(req.getProductId())
            .setRegionId(req.getRegionId())
            .setPlanId(req.getPlanId())
            .setAmount(req.getAmount())
            .setBillingCycle(req.getBillingCycle() != null ? req.getBillingCycle() : FamilyGroupConstants.Defaults.DEFAULT_BILLING_CYCLE)
            .setCurrentMemberCount(FamilyGroupConstants.Defaults.DEFAULT_INITIAL_MEMBER_COUNT) // 初始成员数为1（创建者）
            .setSumVacancy(req.getFamilyGroupTotalVacancy())
            .setGroupLeaderId(currentUserId)
            .setCreateUserId(currentUserId)
            .setCreateTime(currentTime)
            .setUpdateTime(currentTime)
            .setLatestJoinTime(currentTime)
            .setSourceType(FamilyGroupConstants.SourceType.DIRECT_CREATE);

        int result = selfBuiltFamilyGroupMapper.insert(familyGroup);
        if (result <= 0) {
            throw new BusinessException(FamilyGroupConstants.ErrorCode.CREATE_FAILED);
        }

        return familyGroupId;
    }

    /**
     * 创建家庭组成员记录
     */
    private void createGroupLeaderMember(String familyGroupId, String currentUserId, createSelfBuiltFamilyGroupReq req) {
        String memberId = IdGeneratorUtil.generateFamilyMemberId();
        Long currentTime = TimeUtil.getCurrentTimestamp();

        FgMemberPo member = new FgMemberPo()
            .setMemberId(memberId)
            .setFamilyGroupId(familyGroupId)
            .setUserId(currentUserId)
            .setStatus(FamilyGroupConstants.MemberStatus.CONFIRMED) // 团长直接确认状态
            .setInvitationTime(currentTime)
            .setActiveTime(currentTime)
            .setCreateTime(currentTime)
            .setUpdateTime(currentTime);

        int result = fgMemberMapper.insert(member);
        if (result <= 0) {
            throw new BusinessException(FamilyGroupConstants.ErrorCode.CREATE_FAILED);
        }
    }

    /**
     * 创建家庭组成员记录（拼团家庭组）
     */
    private void createGroupLeaderMember(String familyGroupId, String currentUserId, createGroupBuyingFamilyGroupReq req) {
        String memberId = IdGeneratorUtil.generateFamilyMemberId();
        Long currentTime = TimeUtil.getCurrentTimestamp();

        FgMemberPo member = new FgMemberPo()
            .setMemberId(memberId)
            .setFamilyGroupId(familyGroupId)
            .setUserId(currentUserId)
            .setStatus(FamilyGroupConstants.MemberStatus.CONFIRMED) // 团长直接确认状态
            .setInvitationTime(currentTime)
            .setActiveTime(currentTime)
            .setCreateTime(currentTime)
            .setUpdateTime(currentTime);

        int result = fgMemberMapper.insert(member);
        if (result <= 0) {
            throw new BusinessException(FamilyGroupConstants.ErrorCode.CREATE_FAILED);
        }
    }
}
