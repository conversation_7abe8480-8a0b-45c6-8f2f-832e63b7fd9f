package com.subfg.repository.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.subfg.domain.entity.fg.FamilyGroupPo;

/**
 * 家庭组统一 Mapper 接口
 * 支持自建家庭组和拼团家庭组
 */
@Mapper
public interface FamilyGroupMapper extends BaseMapper<FamilyGroupPo> {

    /**
     * 根据家庭组类型和状态查询家庭组列表
     *
     * @param groupType 家庭组类型（1-自建，2-拼团）
     * @param status    家庭组状态
     * @return 家庭组列表
     */
    List<FamilyGroupPo> selectByTypeAndStatus(@Param("groupType") Integer groupType, 
                                              @Param("status") Integer status);

    /**
     * 根据家庭组状态查询家庭组列表
     *
     * @param status 家庭组状态
     * @return 家庭组列表
     */
    List<FamilyGroupPo> selectByStatus(@Param("status") Integer status);

    /**
     * 根据团长ID查询家庭组列表
     *
     * @param groupLeaderId 团长用户ID
     * @param groupType     家庭组类型（可为空）
     * @return 家庭组列表
     */
    List<FamilyGroupPo> selectByGroupLeaderId(@Param("groupLeaderId") String groupLeaderId,
                                              @Param("groupType") Integer groupType);

    /**
     * 根据创建用户ID查询家庭组列表
     *
     * @param createUserId 创建用户ID
     * @param groupType    家庭组类型（可为空）
     * @return 家庭组列表
     */
    List<FamilyGroupPo> selectByCreateUserId(@Param("createUserId") String createUserId,
                                             @Param("groupType") Integer groupType);

    /**
     * 根据产品ID查询家庭组列表
     *
     * @param productId 产品ID
     * @param groupType 家庭组类型（可为空）
     * @return 家庭组列表
     */
    List<FamilyGroupPo> selectByProductId(@Param("productId") Integer productId,
                                          @Param("groupType") Integer groupType);

    /**
     * 根据地区ID查询家庭组列表
     *
     * @param regionId  地区ID
     * @param groupType 家庭组类型（可为空）
     * @return 家庭组列表
     */
    List<FamilyGroupPo> selectByRegionId(@Param("regionId") Integer regionId,
                                         @Param("groupType") Integer groupType);

    /**
     * 查询可加入的家庭组（状态为组建中且未满员）
     *
     * @param productId 产品ID
     * @param regionId  地区ID
     * @param groupType 家庭组类型
     * @return 可加入的家庭组列表
     */
    List<FamilyGroupPo> selectAvailableGroups(@Param("productId") Integer productId,
                                              @Param("regionId") Integer regionId,
                                              @Param("groupType") Integer groupType);

    /**
     * 更新家庭组成员数量
     *
     * @param familyGroupId        家庭组ID
     * @param currentMemberCount   当前成员数
     * @param latestJoinTime       最新加入时间（自建家庭组专用）
     * @return 影响行数
     */
    int updateMemberCount(@Param("familyGroupId") String familyGroupId,
                         @Param("currentMemberCount") Integer currentMemberCount,
                         @Param("latestJoinTime") Long latestJoinTime);

    /**
     * 更新家庭组状态
     *
     * @param familyGroupId 家庭组ID
     * @param status        新状态
     * @param updateTime    更新时间
     * @return 影响行数
     */
    int updateStatus(@Param("familyGroupId") String familyGroupId,
                    @Param("status") Integer status,
                    @Param("updateTime") Long updateTime);

    /**
     * 统计用户创建的家庭组数量
     *
     * @param createUserId 创建用户ID
     * @param status       状态（可为空，为空时统计所有状态）
     * @param groupType    家庭组类型（可为空）
     * @return 家庭组数量
     */
    Integer countByCreateUserId(@Param("createUserId") String createUserId,
                               @Param("status") Integer status,
                               @Param("groupType") Integer groupType);

    /**
     * 统计用户担任团长的家庭组数量
     *
     * @param groupLeaderId 团长用户ID
     * @param status        状态（可为空，为空时统计所有状态）
     * @param groupType     家庭组类型（可为空）
     * @return 家庭组数量
     */
    Integer countByGroupLeaderId(@Param("groupLeaderId") String groupLeaderId,
                                @Param("status") Integer status,
                                @Param("groupType") Integer groupType);

    /**
     * 查询即将到期的家庭组
     *
     * @param expireTime 到期时间阈值
     * @param groupType  家庭组类型（可为空）
     * @return 即将到期的家庭组列表
     */
    List<FamilyGroupPo> selectExpiringSoon(@Param("expireTime") Long expireTime,
                                           @Param("groupType") Integer groupType);

    /**
     * 查询即将截止的拼团家庭组
     *
     * @param deadline 截止时间阈值
     * @return 即将截止的拼团家庭组列表
     */
    List<FamilyGroupPo> selectGroupBuyingNearDeadline(@Param("deadline") Long deadline);

    /**
     * 批量更新家庭组状态
     *
     * @param familyGroupIds 家庭组ID列表
     * @param status         新状态
     * @param updateTime     更新时间
     * @return 影响行数
     */
    int batchUpdateStatus(@Param("familyGroupIds") List<String> familyGroupIds,
                         @Param("status") Integer status,
                         @Param("updateTime") Long updateTime);

    /**
     * 更新拼团家庭组发车时间
     *
     * @param familyGroupId 家庭组ID
     * @param launchTime    发车时间
     * @param status        新状态
     * @param updateTime    更新时间
     * @return 影响行数
     */
    int updateLaunchTime(@Param("familyGroupId") String familyGroupId,
                        @Param("launchTime") Long launchTime,
                        @Param("status") Integer status,
                        @Param("updateTime") Long updateTime);
}
