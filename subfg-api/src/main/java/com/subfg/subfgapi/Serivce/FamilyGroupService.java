package com.subfg.subfgapi.Serivce;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.subfg.common.constans.FamilyGroupConstants;
import com.subfg.common.exception.BusinessException;
import com.subfg.common.util.IdGeneratorUtil;
import com.subfg.common.util.TimeUtil;
import com.subfg.domain.entity.fg.FamilyGroupPo;
import com.subfg.domain.entity.fg.FgMemberPo;
import com.subfg.domain.request.createGroupBuyingFamilyGroupReq;
import com.subfg.domain.request.createSelfBuiltFamilyGroupReq;
import com.subfg.repository.mapper.FamilyGroupMapper;
import com.subfg.repository.mapper.FgMemberMapper;

import cn.dev33.satoken.stp.StpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 家庭组服务类
 * 统一处理自建家庭组和拼团家庭组
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FamilyGroupService {

    private final FamilyGroupMapper familyGroupMapper;
    private final FgMemberMapper fgMemberMapper;

    /**
     * 创建自建家庭组
     */
    @Transactional(rollbackFor = Exception.class)
    public String createSelfBuiltFamilyGroup(createSelfBuiltFamilyGroupReq req) {
        log.info("开始创建自建家庭组，请求参数：{}", req);
        
        String currentUserId = StpUtil.getLoginIdAsString();
        
        // 创建家庭组记录
        String familyGroupId = createSelfBuiltFamilyGroupRecord(req, currentUserId);
        
        // 创建团长成员记录
        createGroupLeaderMember(familyGroupId, currentUserId);
        
        log.info("自建家庭组创建成功，家庭组ID：{}", familyGroupId);
        return familyGroupId;
    }

    /**
     * 创建拼团家庭组
     */
    @Transactional(rollbackFor = Exception.class)
    public String createGroupBuyingFamilyGroup(createGroupBuyingFamilyGroupReq req) {
        log.info("开始创建拼团家庭组，请求参数：{}", req);
        
        String currentUserId = StpUtil.getLoginIdAsString();
        
        // 验证截止时间
        validateDeadline(req.getDeadline());
        
        // 创建家庭组记录
        String familyGroupId = createGroupBuyingFamilyGroupRecord(req, currentUserId);
        
        // 创建团长成员记录
        createGroupLeaderMember(familyGroupId, currentUserId, req);
        
        log.info("拼团家庭组创建成功，家庭组ID：{}", familyGroupId);
        return familyGroupId;
    }

    /**
     * 验证截止时间
     */
    private void validateDeadline(Long deadline) {
        Long currentTime = TimeUtil.getCurrentTimestamp();
        
        // 检查截止时间不能是过去时间
        if (deadline <= currentTime) {
            throw new BusinessException("family.group.deadline.invalid");
        }
        
        // 检查截止时间不能超过30天
        Long maxDeadline = currentTime + (30 * 24 * 60 * 60 * 1000L);
        if (deadline > maxDeadline) {
            throw new BusinessException("family.group.deadline.too.far");
        }
    }

    /**
     * 创建自建家庭组记录
     */
    private String createSelfBuiltFamilyGroupRecord(createSelfBuiltFamilyGroupReq req, String currentUserId) {
        String familyGroupId = IdGeneratorUtil.generateFamilyGroupId();
        Long currentTime = TimeUtil.getCurrentTimestamp();
        
        FamilyGroupPo familyGroup = new FamilyGroupPo()
            .setFamilyGroupId(familyGroupId)
            .setFamilyGroupName(req.getFamilyGroupName().trim())
            .setDescription(req.getDescription() != null ? req.getDescription().trim() : null)
            .setGroupType(FamilyGroupConstants.GroupType.SELF_BUILT) // 设置为自建类型
            .setFamilyGroupStatus(FamilyGroupConstants.Status.REVIEWING) // 设置为审核中
            .setProductId(req.getProductId())
            .setRegionId(req.getRegionId())
            .setPlanId(req.getPlanId())
            .setAmount(req.getAmount())
            .setBillingCycle(req.getBillingCycle() != null ? req.getBillingCycle() : FamilyGroupConstants.Defaults.DEFAULT_BILLING_CYCLE)
            .setCurrentMemberCount(FamilyGroupConstants.Defaults.DEFAULT_INITIAL_MEMBER_COUNT) // 初始成员数为1（创建者）
            .setSumVacancy(req.getFamilyGroupTotalVacancy())
            .setGroupLeaderId(currentUserId)
            .setCreateUserId(currentUserId)
            .setCreateTime(currentTime)
            .setUpdateTime(currentTime)
            .setLatestJoinTime(currentTime);

        int result = familyGroupMapper.insert(familyGroup);
        if (result <= 0) {
            throw new BusinessException(FamilyGroupConstants.ErrorCode.CREATE_FAILED);
        }
        
        return familyGroupId;
    }

    /**
     * 创建拼团家庭组记录
     */
    private String createGroupBuyingFamilyGroupRecord(createGroupBuyingFamilyGroupReq req, String currentUserId) {
        String familyGroupId = IdGeneratorUtil.generateFamilyGroupId();
        Long currentTime = TimeUtil.getCurrentTimestamp();
        
        FamilyGroupPo familyGroup = new FamilyGroupPo()
            .setFamilyGroupId(familyGroupId)
            .setFamilyGroupName(req.getFamilyGroupName().trim())
            .setDescription(req.getDescription() != null ? req.getDescription().trim() : null)
            .setGroupType(FamilyGroupConstants.GroupType.GROUP_BUYING) // 设置为拼团类型
            .setFamilyGroupStatus(FamilyGroupConstants.Status.BUILDING) // 设置为组建中状态
            .setProductId(req.getProductId())
            .setRegionId(req.getRegionId())
            .setPlanId(req.getPlanId())
            .setAmount(req.getAmount()) 
            .setBillingCycle(req.getBillingCycle() != null ? req.getBillingCycle() : FamilyGroupConstants.Defaults.DEFAULT_BILLING_CYCLE)
            .setCurrentMemberCount(FamilyGroupConstants.Defaults.DEFAULT_INITIAL_MEMBER_COUNT) // 初始成员数为1（创建者）
            .setSumVacancy(req.getFamilyGroupTotalVacancy())
            .setDeadline(req.getDeadline()) // 设置截止时间
            .setGroupLeaderId(null) // 拼团家庭组创建时没有团长
            .setCreateUserId(currentUserId)
            .setCreateTime(currentTime)
            .setUpdateTime(currentTime);

        int result = familyGroupMapper.insert(familyGroup);
        if (result <= 0) {
            throw new BusinessException(FamilyGroupConstants.ErrorCode.CREATE_FAILED);
        }
        
        return familyGroupId;
    }

    /**
     * 创建团长成员记录（自建家庭组）
     */
    private void createGroupLeaderMember(String familyGroupId, String currentUserId) {
        String memberId = IdGeneratorUtil.generateFamilyMemberId();
        Long currentTime = TimeUtil.getCurrentTimestamp();
        
        FgMemberPo member = new FgMemberPo()
            .setMemberId(memberId)
            .setFamilyGroupId(familyGroupId)
            .setUserId(currentUserId)
            .setStatus(FamilyGroupConstants.MemberStatus.ACTIVATED) // 团长直接激活
            .setCreateTime(currentTime)
            .setUpdateTime(currentTime);

        int result = fgMemberMapper.insert(member);
        if (result <= 0) {
            throw new BusinessException(FamilyGroupConstants.ErrorCode.CREATE_FAILED);
        }
    }

    /**
     * 创建团长成员记录（拼团家庭组）
     */
    private void createGroupLeaderMember(String familyGroupId, String currentUserId, createGroupBuyingFamilyGroupReq req) {
        String memberId = IdGeneratorUtil.generateFamilyMemberId();
        Long currentTime = TimeUtil.getCurrentTimestamp();
        
        FgMemberPo member = new FgMemberPo()
            .setMemberId(memberId)
            .setFamilyGroupId(familyGroupId)
            .setUserId(currentUserId)
            .setStatus(FamilyGroupConstants.MemberStatus.PENDING) // 拼团所有成员都是待激活 有团长后统一全部激活
            .setCreateTime(currentTime)
            .setUpdateTime(currentTime);

        int result = fgMemberMapper.insert(member);
        if (result <= 0) {
            throw new BusinessException(FamilyGroupConstants.ErrorCode.CREATE_FAILED);
        }
    }
}
