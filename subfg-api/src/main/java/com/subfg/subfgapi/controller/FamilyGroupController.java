package com.subfg.subfgapi.controller;

import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.subfg.domain.request.createGroupBuyingFamilyGroupReq;
import com.subfg.domain.request.createSelfBuiltFamilyGroupReq;
import com.subfg.domain.request.getFamilyGroupListReq;
import com.subfg.domain.vo.FamilyGroupVo;
import com.subfg.domain.vo.Result;
import com.subfg.subfgapi.Serivce.FamilyGroupService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v3/familyGroup")
@Tag(name = "家庭群组管理", description = "家庭群组管理相关接口")
public class FamilyGroupController {

    private final FamilyGroupService familyGroupService;

    /**
     * 创建自建家庭组
     * @param req
     * @return
     */
    @Operation(summary = "创建自建家庭群组", description = "创建自建家庭群组")
    @PostMapping("/createSelfBuiltFamilyGroup")
    public Result<String> createSelfBuiltFamilyGroup(@Valid @RequestBody createSelfBuiltFamilyGroupReq req){
        String familyGroupId = familyGroupService.createSelfBuiltFamilyGroup(req);
        return Result.success(familyGroupId);
    }

    /**
     * 创建拼团家庭组
     * @param req
     * @return
     */
    @Operation(summary = "创建拼团家庭群组", description = "创建拼团家庭群组")
    @PostMapping("/createGroupBuyingFamilyGroup")
    public Result<String> createGroupBuyingFamilyGroup(@Valid @RequestBody createGroupBuyingFamilyGroupReq req){
        String familyGroupId = familyGroupService.createGroupBuyingFamilyGroup(req);
        return Result.success(familyGroupId);
    }

    /**
     * 获取家庭组分页列表
     */
    @Operation(summary = "获取家庭组分页列表", description = "获取家庭组分页列表")
    @GetMapping("/getFamilyGroupList")
    public Result<List<FamilyGroupVo>> getFamilyGroupList(@Valid getFamilyGroupListReq req){
        List<FamilyGroupVo> list = familyGroupService.getFamilyGroupList(req);
        return Result.success(list);
    }
    
    /**
     * 获取推荐家庭组列表
     */
    @Operation(summary = "获取推荐家庭组列表", description = "获取推荐家庭组列表")
    @GetMapping("/getRecommendFamilyGroupList")
    public Result<List<FamilyGroupVo>> getRecommendFamilyGroupList(){
        List<FamilyGroupVo> list = familyGroupService.getRecommendFamilyGroupList();
        return Result.success(list);
    }

}
