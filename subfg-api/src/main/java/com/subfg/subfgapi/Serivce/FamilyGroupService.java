package com.subfg.subfgapi.Serivce;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.subfg.common.constans.FamilyGroupConstants;
import com.subfg.common.exception.BusinessException;
import com.subfg.common.util.IdGeneratorUtil;
import com.subfg.common.util.TimeUtil;
import com.subfg.domain.entity.fg.FgMemberPo;
import com.subfg.domain.entity.fg.SelfBuiltFamilyGroupPo;
import com.subfg.domain.request.createGroupBuyingFamilyGroupReq;
import com.subfg.domain.request.createSelfBuiltFamilyGroupReq;
import com.subfg.repository.mapper.FgMemberMapper;
import com.subfg.repository.mapper.SelfBuiltFamilyGroupMapper;

import cn.dev33.satoken.stp.StpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class FamilyGroupService {

    private final SelfBuiltFamilyGroupMapper selfBuiltFamilyGroupMapper;
    private final FgMemberMapper fgMemberMapper;

    /**
     * 创建自建家庭组
     */
    @Transactional(rollbackFor = Exception.class)
    public String createSelfBuiltFamilyGroup(createSelfBuiltFamilyGroupReq req) {
        log.info("开始创建自建家庭组，请求参数：{}", req);

        // 1. 获取当前登录用户ID
        String currentUserId = StpUtil.getLoginIdAsString();

        // 2. 创建家庭组
        String familyGroupId = createFamilyGroup(req, currentUserId);

        // 3. 创建家庭组成员记录
        createGroupLeaderMember(familyGroupId, currentUserId, req);

        log.info("自建家庭组创建成功，家庭组ID：{}", familyGroupId);
        return familyGroupId;
    }

    /**
     * 创建拼团家庭组
     */
    public void createGroupBuyingFamilyGroup(createGroupBuyingFamilyGroupReq req){
        // 拼团家庭组创建逻辑
    }

    /**
     * 创建家庭组记录
     */
    private String createFamilyGroup(createSelfBuiltFamilyGroupReq req, String currentUserId) {
        String familyGroupId = IdGeneratorUtil.generateFamilyGroupId();
        Long currentTime = TimeUtil.getCurrentTimestamp();

        SelfBuiltFamilyGroupPo familyGroup = new SelfBuiltFamilyGroupPo()
            .setFamilyGroupId(familyGroupId)
            .setFamilyGroupName(req.getFamilyGroupName().trim())
            .setDescription(req.getDescription() != null ? req.getDescription().trim() : null)
            .setFamilyGroupStatus(FamilyGroupConstants.Status.REVIEWING) // 设置为审核中
            .setProductId(req.getProductId())
            .setRegionId(req.getRegionId())
            .setPlanId(req.getPlanId())
            .setAmount(req.getAmount())
            .setBillingCycle(req.getBillingCycle() != null ? req.getBillingCycle() : FamilyGroupConstants.Defaults.DEFAULT_BILLING_CYCLE)
            .setCurrentMemberCount(FamilyGroupConstants.Defaults.DEFAULT_INITIAL_MEMBER_COUNT) // 初始成员数为1（创建者）
            .setSumVacancy(req.getFamilyGroupTotalVacancy())
            .setGroupLeaderId(currentUserId)
            .setCreateUserId(currentUserId)
            .setCreateTime(currentTime)
            .setUpdateTime(currentTime)
            .setLatestJoinTime(currentTime)
            .setSourceType(FamilyGroupConstants.SourceType.DIRECT_CREATE);

        int result = selfBuiltFamilyGroupMapper.insert(familyGroup);
        if (result <= 0) {
            throw new BusinessException(FamilyGroupConstants.ErrorCode.CREATE_FAILED);
        }

        return familyGroupId;
    }

    /**
     * 创建家庭组成员记录
     */
    private void createGroupLeaderMember(String familyGroupId, String currentUserId, createSelfBuiltFamilyGroupReq req) {
        String memberId = IdGeneratorUtil.generateFamilyMemberId();
        Long currentTime = TimeUtil.getCurrentTimestamp();

        FgMemberPo member = new FgMemberPo()
            .setMemberId(memberId)
            .setFamilyGroupId(familyGroupId)
            .setUserId(currentUserId)
            .setStatus(FamilyGroupConstants.MemberStatus.CONFIRMED) // 团长直接确认状态
            .setInvitationTime(currentTime)
            .setActiveTime(currentTime)
            .setCreateTime(currentTime)
            .setUpdateTime(currentTime);

        int result = fgMemberMapper.insert(member);
        if (result <= 0) {
            throw new BusinessException(FamilyGroupConstants.ErrorCode.CREATE_FAILED);
        }
    }
}
