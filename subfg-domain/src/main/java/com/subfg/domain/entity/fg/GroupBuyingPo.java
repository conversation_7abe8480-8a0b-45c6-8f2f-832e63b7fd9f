package com.subfg.domain.entity.fg;

import java.io.Serializable;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 拼团家庭组实体类
 * 专门处理拼团业务流程
 * 对应数据库表：fg_group_buying
 *
 * <AUTHOR>
 * @since 2025-01-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("fg_group_buying")
public class GroupBuyingPo implements Serializable {

    private static final long serialVersionUID = 1L;

    // ==================== 基本信息 ====================

    /**
     * 拼团ID（主键）
     */
    @TableId(value = "group_buying_id", type = IdType.INPUT)
    private String groupBuyingId;

    /**
     * 拼团名称
     */
    @TableField("group_buying_name")
    private String groupBuyingName;

    /**
     * 拼团描述
     */
    @TableField("description")
    private String description;

    /**
     * 拼团状态（0-审核中，1-组建中，2-已发车，3-拼团失败（截止发车时间未发车或无团长）,-1-审核未通过）
     */
    @TableField("group_buying_status")
    private Integer groupBuyingStatus;

    // ==================== 产品信息 ====================

    /**
     * 产品ID
     */
    @TableField("product_id")
    private Integer productId;

    /**
     * 产品分类ID
     */
    @TableField("category_id")
    private String categoryId;

    /**
     * 地区ID
     */
    @TableField("region_id")
    private Integer regionId;

    /**
     * 套餐ID
     */
    @TableField("plan_id")
    private String planId;

    /**
     * 原价
     */
    @TableField("original_amount")
    private BigDecimal originalAmount;

    /**
     * 拼团价
     */
    @TableField("group_amount")
    private BigDecimal groupAmount;

    /**
     * 计费周期（月）
     */
    @TableField("billing_cycle")
    private Integer billingCycle;

    // ==================== 拼团规则 ====================

    /**
     * 当前参团人数
     */
    @TableField("current_member_count")
    private Integer currentMemberCount;

    /**
     * 总空位数
     */
    @TableField("sum_vacancy")
    private Integer sumVacancy;

    /**
     * 拼团截止时间
     */
    @TableField("deadline")
    private Long deadline;

    // ==================== 用户信息 ====================

    /**
     * 团长用户ID
     */
    @TableField("group_leader_id")
    private String groupLeaderId;

    /**
     * 创建用户ID
     */
    @TableField("create_user_id")
    private String createUserId;

    // ==================== 时间信息 ====================

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Long updateTime;

    /**
     * 发车时间
     */
    @TableField("launch_time")
    private Long launchTime;

    /**
     * 转为自建时间
     */
    @TableField("convert_time")
    private Long convertTime;

    /**
     * 订阅开始时间
     */
    @TableField("subscribe_start_time")
    private Long subscribeStartTime;

    /**
     * 订阅结束时间
     */
    @TableField("subscribe_end_time")
    private Long subscribeEndTime;

    // ==================== 审核信息 ====================

    /**
     * 审核时间
     */
    @TableField("review_time")
    private Long reviewTime;

    /**
     * 审核用户
     */
    @TableField("review_user")
    private String reviewUser;

    /**
     * 审核备注
     */
    @TableField("review_remark")
    private String reviewRemark;

    // ==================== 转换信息 ====================

    /**
     * 转换后的自建家庭组ID
     */
    @TableField("converted_family_group_id")
    private String convertedFamilyGroupId;

    /**
     * 是否已转换（0-未转换，1-已转换）
     */
    @TableField("is_converted")
    private Integer isConverted;

}
