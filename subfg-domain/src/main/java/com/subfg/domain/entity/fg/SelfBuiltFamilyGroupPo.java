package com.subfg.domain.entity.fg;

import java.io.Serializable;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 自建家庭组实体类
 * 专门处理自建家庭组业务
 * 对应数据库表：fg_family_group
 *
 * <AUTHOR>
 * @since 2025-01-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("fg_family_group")
public class SelfBuiltFamilyGroupPo implements Serializable {

    private static final long serialVersionUID = 1L;

    // ==================== 基本信息 ====================

    /**
     * 家庭组ID（主键）
     */
    @TableId(value = "family_group_id", type = IdType.INPUT)
    private String familyGroupId;

    /**
     * 家庭组名称
     */
    @TableField("family_group_name")
    private String familyGroupName;

    /**
     * 家庭组描述
     */
    @TableField("description")
    private String description;

    /**
     * 家庭组状态（0-审核中，1-组建中，2-发车中，3-关闭（团长关闭），-1-审核未通过）
     */
    @TableField("family_group_status")
    private Integer familyGroupStatus;

    /**
     * 邀请链接
     */
    @TableField("invite_url")
    private String inviteUrl;

    // ==================== 产品信息 ====================

    /**
     * 产品ID
     */
    @TableField("product_id")
    private Integer productId;

    /**
     * 产品分类ID
     */
    @TableField("category_id")
    private String categoryId;

    /**
     * 地区ID
     */
    @TableField("region_id")
    private Integer regionId;

    /**
     * 套餐ID
     */
    @TableField("plan_id")
    private Integer planId;

    /**
     * 订阅金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 计费周期（月）
     */
    @TableField("billing_cycle")
    private Integer billingCycle;

    // ==================== 成员管理 ====================

    /**
     * 当前成员数
     */
    @TableField("current_member_count")
    private Integer currentMemberCount;

    /**
     * 总空位数
     */
    @TableField("sum_vacancy")
    private Integer sumVacancy;

    // ==================== 用户信息 ====================

    /**
     * 团长用户ID（自建默认创建者为团长）
     */
    @TableField("group_leader_id")
    private String groupLeaderId;

    /**
     * 创建用户ID
     */
    @TableField("create_user_id")
    private String createUserId;

    // ==================== 时间信息 ====================

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Long updateTime;

    /**
     * 最新加入时间
     */
    @TableField("latest_join_time")
    private Long latestJoinTime;

    /**
     * 订阅开始时间
     */
    @TableField("subscribe_start_time")
    private Long subscribeStartTime;

    /**
     * 订阅结束时间
     */
    @TableField("subscribe_end_time")
    private Long subscribeEndTime;

    /**
     * 截止发车时间（拼团专用）
     */
    @TableField("deadline")
    private Long deadline;

    // ==================== 审核信息 ====================

    /**
     * 审核时间
     */
    @TableField("review_time")
    private Long reviewTime;

    /**
     * 审核用户
     */
    @TableField("review_user")
    private String reviewUser;

    /**
     * 审核备注
     */
    @TableField("review_remark")
    private String reviewRemark;

    /**
     * 审核图片
     */
    @TableField("review_picture")
    private String reviewPicture;

    // ==================== 来源信息 ====================

    /**
     * 来源类型（1-直接创建，2-拼团转换）
     */
    @TableField("source_type")
    private Integer sourceType;

    /**
     * 来源拼团ID（如果是拼团转换而来）
     */
    @TableField("source_group_buying_id")
    private String sourceGroupBuyingId;

    /**
     * 转换时间（如果是拼团转换而来）
     */
    @TableField("convert_time")
    private Long convertTime;

}
