package com.subfg.repository.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.subfg.domain.entity.fg.SelfBuiltFamilyGroupPo;

/**
 * 自建家庭组 Mapper 接口
 */
@Mapper
public interface SelfBuiltFamilyGroupMapper extends BaseMapper<SelfBuiltFamilyGroupPo> {

    /**
     * 根据家庭组状态查询家庭组列表
     *
     * @param status 家庭组状态
     * @return 家庭组列表
     */
    List<SelfBuiltFamilyGroupPo> selectByStatus(@Param("status") Integer status);

    /**
     * 根据团长ID查询家庭组列表
     *
     * @param groupLeaderId 团长用户ID
     * @return 家庭组列表
     */
    List<SelfBuiltFamilyGroupPo> selectByGroupLeaderId(@Param("groupLeaderId") String groupLeaderId);

    /**
     * 根据创建用户ID查询家庭组列表
     *
     * @param createUserId 创建用户ID
     * @return 家庭组列表
     */
    List<SelfBuiltFamilyGroupPo> selectByCreateUserId(@Param("createUserId") String createUserId);

    /**
     * 根据产品ID查询家庭组列表
     *
     * @param productId 产品ID
     * @return 家庭组列表
     */
    List<SelfBuiltFamilyGroupPo> selectByProductId(@Param("productId") Integer productId);

    /**
     * 根据地区ID查询家庭组列表
     *
     * @param regionId 地区ID
     * @return 家庭组列表
     */
    List<SelfBuiltFamilyGroupPo> selectByRegionId(@Param("regionId") Integer regionId);

    /**
     * 查询可加入的家庭组（状态为组建中且未满员）
     *
     * @param productId 产品ID
     * @param regionId  地区ID
     * @return 可加入的家庭组列表
     */
    List<SelfBuiltFamilyGroupPo> selectAvailableGroups(@Param("productId") Integer productId,
                                                       @Param("regionId") Integer regionId);

    /**
     * 更新家庭组成员数量
     *
     * @param familyGroupId        家庭组ID
     * @param currentMemberCount   当前成员数
     * @param latestJoinTime       最新加入时间
     * @return 影响行数
     */
    int updateMemberCount(@Param("familyGroupId") String familyGroupId,
                         @Param("currentMemberCount") Integer currentMemberCount,
                         @Param("latestJoinTime") Long latestJoinTime);

    /**
     * 更新家庭组状态
     *
     * @param familyGroupId 家庭组ID
     * @param status        新状态
     * @param updateTime    更新时间
     * @return 影响行数
     */
    int updateStatus(@Param("familyGroupId") String familyGroupId,
                    @Param("status") Integer status,
                    @Param("updateTime") Long updateTime);

    /**
     * 统计用户创建的家庭组数量
     *
     * @param createUserId 创建用户ID
     * @param status       状态（可为空，为空时统计所有状态）
     * @return 家庭组数量
     */
    Integer countByCreateUserId(@Param("createUserId") String createUserId,
                               @Param("status") Integer status);

    /**
     * 统计用户担任团长的家庭组数量
     *
     * @param groupLeaderId 团长用户ID
     * @param status        状态（可为空，为空时统计所有状态）
     * @return 家庭组数量
     */
    Integer countByGroupLeaderId(@Param("groupLeaderId") String groupLeaderId,
                                @Param("status") Integer status);

    /**
     * 查询即将到期的家庭组
     *
     * @param expireTime 到期时间阈值
     * @return 即将到期的家庭组列表
     */
    List<SelfBuiltFamilyGroupPo> selectExpiringSoon(@Param("expireTime") Long expireTime);

    /**
     * 批量更新家庭组状态
     *
     * @param familyGroupIds 家庭组ID列表
     * @param status         新状态
     * @param updateTime     更新时间
     * @return 影响行数
     */
    int batchUpdateStatus(@Param("familyGroupIds") List<String> familyGroupIds,
                         @Param("status") Integer status,
                         @Param("updateTime") Long updateTime);
}
