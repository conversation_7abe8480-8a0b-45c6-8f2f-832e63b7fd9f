package com.subfg.subfgapi.Serivce;

import org.springframework.stereotype.Service;

import com.subfg.domain.request.createGroupBuyingFamilyGroupReq;
import com.subfg.domain.request.createSelfBuiltFamilyGroupReq;

@Service
public class FamilyGroupService {

    /**
     * 创建自建家庭组
     */
    public void createSelfBuiltFamilyGroup(createSelfBuiltFamilyGroupReq req){
        // 自建家庭组创建逻辑
    }

    /**
     * 创建拼团家庭组
     */
    public void createGroupBuyingFamilyGroup(createGroupBuyingFamilyGroupReq req){
        // 拼团家庭组创建逻辑
    }
}
