package com.subfg.domain.request;

import java.math.BigDecimal;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(description = "创建拼团家庭组请求")
public class createGroupBuyingFamilyGroupReq {
    /**
     * 家庭组名称
     */
    private String familyGroupName;

    /**
     * 家庭组描述
     */
    private String description;

    /**
     * 产品ID
     */
    private Integer productId;

    /**
     * 地区ID
     */
    private Integer regionId;

    /**
     * 套餐ID
     */
    private Integer planId;

    /**
     * 家庭组总空位数
     */
    private Integer familyGroupTotalVacancy;

    /**
     * 计费周期
     */
    private Integer billingCycle;
    
    /**
     * 价格
     */
    private BigDecimal amount;

    /**
     * 截止发车时间
     */
    private Long deadline;
}
